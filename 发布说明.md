# 观测岗位练兵试题管理系统 - 发布包

## 系统简介

观测岗位练兵试题管理系统是一个专门用于管理和处理观测岗位练兵试题的桌面应用程序。系统支持从Word文档批量导入试题，自动去重，并提供便捷的试题管理功能。

## 系统特性

- **批量导入**：支持从Word文档(.docx)批量导入试题
- **智能去重**：自动识别和去除重复试题
- **进度显示**：实时显示导入进度和处理状态
- **数据管理**：支持试题的查看、搜索和管理
- **数据导出**：支持将试题导出为Excel格式
- **用户友好**：简洁直观的图形界面

## 文件说明

- `观测岗位练兵试题管理系统.exe` - 主程序文件（独立可执行文件）
- `用户手册.md` - 详细使用说明
- `README.md` - 项目说明文档
- `发布说明.md` - 本文件，发布包说明

## 系统要求

- **操作系统**：Windows 7/8/10/11 (64位)
- **内存**：建议4GB以上
- **硬盘空间**：至少100MB可用空间
- **其他**：无需安装Python或其他依赖

## 安装说明

本程序为绿色软件，无需安装：

1. 将整个发布包解压到任意目录
2. 双击 `观测岗位练兵试题管理系统.exe` 即可运行
3. 首次运行会自动创建必要的配置文件

## 快速开始

1. **启动程序**：双击exe文件启动系统
2. **导入试题**：点击"导入Word文档"按钮，选择包含试题的Word文档
3. **查看结果**：导入完成后，可在试题列表中查看所有试题
4. **导出数据**：如需要，可将试题导出为Excel文件

## 注意事项

- 请确保Word文档格式正确，试题按标准格式编写
- 导入大量试题时请耐心等待，系统会显示进度
- 建议定期备份试题数据文件(question_bank.json)
- 如遇到问题，请查看用户手册或联系技术支持

## 技术支持

如有问题或建议，请联系开发团队。

## 版本信息

- **版本号**：v2.0
- **发布日期**：2025年7月27日
- **开发环境**：Python 3.12 + Tkinter
- **打包工具**：PyInstaller 6.14.1

## 更新日志

### v2.0 (2025-07-27)
- ✅ 修复了lxml模块缺失导致的运行错误
- ✅ 优化了打包配置，排除了不必要的依赖
- ✅ 减小了程序体积，提高了启动速度
- ✅ 改进了启动脚本，支持自动检测可执行文件
- ✅ 更新了文档和使用说明

### v1.0 (2025-07-26)
- ✅ 基础功能实现
- ✅ 支持docx文档导入
- ✅ 去重和清理功能
- ✅ Excel导出功能

---

© 2025 观测岗位练兵试题管理系统开发团队
