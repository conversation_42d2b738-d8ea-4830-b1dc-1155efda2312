# 岗位练兵 - 试题管理系统

## 功能特点

### 🎯 核心功能
- **导入试题文档**：支持导入docx格式的试题文档
- **智能提取题目**：自动识别题目、选项、答案和解析
- **去重功能**：自动检测并去除重复题目
- **清理答案**：删除考生答案，保留正确答案
- **题库管理**：查看、导出题库数据

### 📊 数据处理
- 自动识别题目编号（数字+点 或 括号数字）
- 识别选项（A-D选项）
- 提取答案和解析
- 相似度检测去除重复题目

### 💾 数据存储
- 本地JSON格式存储题库
- 支持导出到Excel格式
- 包含题目统计信息

## 安装依赖

```bash
pip install -r requirements.txt
```

## 使用方法

### 1. 运行程序
```bash
python main.py
```

### 2. 导入试题文档
- 点击"导入试题文档 (docx)"按钮
- 选择要导入的docx文件
- 系统会自动处理并导入题目

### 3. 查看题库
- 点击"查看当前题库"按钮
- 在新窗口中浏览所有题目

### 4. 导出题库
- 点击"导出题库到Excel"按钮
- 选择保存位置
- 导出完整的题库数据

## 支持的文档格式

### 题目格式示例：
```
1. 这是一道选择题的题目内容？
A. 选项A
B. 选项B  
C. 选项C
D. 选项D
答案：A
解析：这是解析内容

2. 第二道题目...
```

### 支持的编号格式：
- 数字+点：`1.` `2.` `3.`
- 数字+括号：`1)` `2)` `3)`
- 中文数字+括号：`(一)` `(二)` `(三)`

## 系统要求

- Python 3.7+
- Windows/macOS/Linux
- 支持docx格式的Word文档

## 注意事项

1. 确保docx文档格式规范，题目编号清晰
2. 系统会自动检测重复题目，相似度阈值设为80%
3. 导入的题目会自动清理，去除答案和解析行
4. 题库数据保存在`question_bank.json`文件中

## 技术栈

- **GUI框架**：tkinter
- **文档处理**：python-docx
- **数据处理**：pandas
- **Excel导出**：openpyxl
- **相似度检测**：difflib

## 可执行文件版本

### 使用可执行文件（推荐）
为了方便用户使用，我们提供了独立的可执行文件版本：

1. **直接运行**：双击 `dist\观测岗位练兵试题管理系统.exe`
2. **使用启动脚本**：双击 `启动系统.bat`

### 可执行文件优势
- 无需安装Python环境
- 包含所有必要依赖
- 一键启动，使用简便
- 适合非技术用户

### 文件说明
- `dist\观测岗位练兵试题管理系统.exe` - 主程序可执行文件
- `启动系统.bat` - 启动脚本，自动检测并运行程序
- `main.py` - Python源代码（开发者使用）

## 更新日志

### v2.0.0 (2025-07-27)
- 修复了lxml模块缺失问题
- 创建了独立可执行文件版本
- 优化了打包配置，减小程序体积
- 改进了启动脚本，支持自动检测
- 排除了不必要的依赖包

### v1.0.0
- 基础功能实现
- 支持docx文档导入
- 去重和清理功能
- Excel导出功能